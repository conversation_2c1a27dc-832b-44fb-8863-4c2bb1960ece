use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::Path;
use qcos::client::Client as CosClient;
use qcos::acl::{AclHeader, ObjectAcl};

// 腾讯云COS配置结构
#[derive(Serialize, Deserialize, Clone)]
struct TencentCosConfig {
    region: String,
    secret_id: String,
    secret_key: String,
    bucket_name: String,
    custom_domain: Option<String>,
}



// 豆包图像编辑任务创建请求 (火山方舟官方格式)
#[derive(Debug, Serialize)]
struct DouBaoImageEditRequest {
    model: String,
    prompt: String,
    image: String,
    response_format: String,
    size: String,
    seed: Option<i64>,
    guidance_scale: Option<f32>,
    watermark: Option<bool>,
}

// 豆包任务创建响应
#[derive(Debug, Serialize, Deserialize)]
struct DouBaoTaskResponse {
    task_id: String,
    status: String,
    message: Option<String>,
}

// 阿里云OutfitAnyone换衣任务创建请求
#[derive(Debug, Serialize)]
struct OutfitAnyoneRequest {
    model: String,
    input: OutfitAnyoneInput,
    parameters: OutfitAnyoneParameters,
}

#[derive(Debug, Serialize)]
struct OutfitAnyoneInput {
    person_image_url: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    top_garment_url: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    bottom_garment_url: Option<String>,
}

#[derive(Debug, Serialize)]
struct OutfitAnyoneParameters {
    resolution: i32,
    restore_face: bool,
}

// 阿里云OutfitAnyone任务创建响应
#[derive(Debug, Serialize, Deserialize)]
struct OutfitAnyoneTaskResponse {
    output: OutfitAnyoneTaskOutput,
    request_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct OutfitAnyoneTaskOutput {
    task_id: String,
    task_status: String,
}

// 阿里云OutfitAnyone任务查询响应
#[derive(Debug, Serialize, Deserialize)]
struct OutfitAnyoneResultResponse {
    output: OutfitAnyoneResultOutput,
    request_id: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    usage: Option<OutfitAnyoneUsage>,
}

#[derive(Debug, Serialize, Deserialize)]
struct OutfitAnyoneResultOutput {
    task_id: String,
    task_status: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    image_url: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    submit_time: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    scheduled_time: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    end_time: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    code: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    message: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct OutfitAnyoneUsage {
    image_count: i32,
}

// 豆包任务查询响应
#[derive(Debug, Serialize, Deserialize)]
struct DouBaoTaskResultResponse {
    task_id: String,
    status: String,
    result: Option<DouBaoImageEditResult>,
    message: Option<String>,
    created_at: Option<String>,
    finished_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct DouBaoImageEditResult {
    image_url: String,
    width: Option<i32>,
    height: Option<i32>,
}

// 前端传递的参数结构
#[derive(Debug, Serialize, Deserialize)]
struct UploadFileRequest {
    file_path: String,
    model_name: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct ImageEditRequest {
    image_url: String,
    function: String,
    prompt: String,
    mask_image_url: Option<String>,
    parameters: Option<serde_json::Value>,
}

// 上传文件到腾讯云COS
async fn upload_file_to_cos(cos_config: &TencentCosConfig, file_path: &str) -> Result<String> {
    let file_name = Path::new(file_path)
        .file_name()
        .and_then(|name| name.to_str())
        .ok_or_else(|| anyhow::anyhow!("Invalid file path"))?;

    // 创建COS客户端
    let client = CosClient::new(
        &cos_config.secret_id,
        &cos_config.secret_key,
        &cos_config.bucket_name,
        &cos_config.region,
    );

    // 生成唯一的对象键
    let object_key = format!("uploads/{}", file_name);

    // 设置ACL权限为公共读取
    let mut acl_header = AclHeader::new();
    acl_header.insert_object_x_cos_acl(ObjectAcl::PublicRead);

    // 上传文件
    let file_path_buf = std::path::PathBuf::from(file_path);
    let result = client
        .put_object(&file_path_buf, &object_key, None, Some(acl_header))
        .await;

    if result.error_no != qcos::objects::ErrNo::SUCCESS {
        return Err(anyhow::anyhow!("Failed to upload to COS: [{}] {}", result.error_no, result.error_message));
    }

    // 构建文件URL
    let file_url = if let Some(custom_domain) = &cos_config.custom_domain {
        format!("https://{}/{}", custom_domain, object_key)
    } else {
        format!("https://{}.cos.{}.myqcloud.com/{}", cos_config.bucket_name, cos_config.region, object_key)
    };

    // 验证文件是否可以访问
    println!("验证文件URL访问性: {}", file_url);
    let test_client = reqwest::Client::new();
    match test_client.head(&file_url).send().await {
        Ok(response) => {
            println!("文件访问测试 - 状态码: {}", response.status());
            if response.status().is_success() {
                println!("✅ 文件可以公开访问");
            } else {
                println!("❌ 文件无法公开访问，状态码: {}", response.status());
            }
        }
        Err(e) => {
            println!("❌ 文件访问测试失败: {}", e);
        }
    }

    Ok(file_url)
}



// Tauri命令：上传文件并获取URL
#[tauri::command]
async fn upload_file_and_get_url(
    region: String,
    secret_id: String,
    secret_key: String,
    bucket_name: String,
    custom_domain: Option<String>,
    file_path: String,
) -> Result<String, String> {
    println!("开始上传文件到腾讯云COS: {}", file_path);
    println!("使用存储桶: {}", bucket_name);
    println!("区域: {}", region);

    // 创建腾讯云COS配置
    let cos_config = TencentCosConfig {
        region,
        secret_id,
        secret_key,
        bucket_name,
        custom_domain,
    };

    // 上传文件到腾讯云COS
    let cos_url = upload_file_to_cos(&cos_config, &file_path)
        .await
        .map_err(|e| format!("Failed to upload file to COS: {}", e))?;

    println!("文件上传成功: {}", cos_url);
    Ok(cos_url)
}

// 创建豆包图像编辑任务 (使用火山方舟官方API)
async fn create_doubao_image_edit_task(
    api_key: &str,
    image_url: &str,
    prompt: &str,
    strength: f32,
    guidance_scale: f32,
    _num_inference_steps: i32,
    seed: Option<i64>,
) -> Result<String> {
    println!("开始创建豆包图像编辑任务");
    println!("图片URL: {}", image_url);
    println!("提示词: {}", prompt);
    println!("编辑强度: {}", strength);
    println!("引导比例: {}", guidance_scale);

    let client = reqwest::Client::new();
    let url = "https://ark.cn-beijing.volces.com/api/v3/images/generations";

    let request_body = DouBaoImageEditRequest {
        model: "doubao-seededit-3-0-i2i-250628".to_string(),
        prompt: prompt.to_string(),
        image: image_url.to_string(),
        response_format: "url".to_string(),
        size: "adaptive".to_string(),
        seed,
        guidance_scale: Some(guidance_scale),
        watermark: Some(true),
    };

    println!("请求体: {}", serde_json::to_string_pretty(&request_body).unwrap_or_default());

    let response = client
        .post(url)
        .header("Authorization", format!("Bearer {}", api_key))
        .header("Content-Type", "application/json")
        .json(&request_body)
        .send()
        .await?;

    let status = response.status();
    let response_text = response.text().await?;

    println!("创建任务响应状态: {}", status);
    println!("创建任务响应内容: {}", response_text);

    if !status.is_success() {
        return Err(anyhow::anyhow!("Failed to create image edit task: {}", response_text));
    }

    // 解析响应
    let task_response: serde_json::Value = serde_json::from_str(&response_text)
        .map_err(|e| anyhow::anyhow!("Failed to parse task response: {}. Response was: {}", e, response_text))?;

    // 检查是否有错误
    if let Some(error) = task_response.get("error") {
        return Err(anyhow::anyhow!("API Error: {}", error));
    }

    // 检查是否有生成的图片
    if let Some(data) = task_response.get("data").and_then(|d| d.as_array()) {
        if let Some(first_image) = data.first() {
            if let Some(url) = first_image.get("url").and_then(|u| u.as_str()) {
                println!("图像编辑完成，结果URL: {}", url);
                return Ok(url.to_string());
            }
        }
    }

    Err(anyhow::anyhow!("No image URL found in response"))
}

// 查询豆包图像编辑任务结果
async fn get_doubao_image_edit_result(api_key: &str, task_id: &str) -> Result<DouBaoTaskResultResponse> {
    println!("查询豆包图像编辑任务结果，任务ID: {}", task_id);

    let client = reqwest::Client::new();
    let url = format!("https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks/{}", task_id);

    let response = client
        .get(&url)
        .header("Authorization", format!("Bearer {}", api_key))
        .send()
        .await?;

    let status = response.status();
    let response_text = response.text().await?;

    println!("查询任务响应状态: {}", status);
    println!("查询任务响应内容: {}", response_text);

    if !status.is_success() {
        return Err(anyhow::anyhow!("Failed to get image edit task result: {}", response_text));
    }

    let result: DouBaoTaskResultResponse = serde_json::from_str(&response_text)
        .map_err(|e| anyhow::anyhow!("Failed to parse image edit task result: {}. Response was: {}", e, response_text))?;

    println!("任务状态: {}", result.status);
    Ok(result)
}

// Tauri命令：创建豆包图像编辑任务 (火山方舟官方API)
#[tauri::command]
async fn create_doubao_image_edit(
    api_key: String,
    image_url: String,
    prompt: String,
    strength: f32,
    guidance_scale: f32,
    num_inference_steps: i32,
    seed: Option<i64>,
) -> Result<String, String> {
    let result_url = create_doubao_image_edit_task(
        &api_key,
        &image_url,
        &prompt,
        strength,
        guidance_scale,
        num_inference_steps,
        seed,
    )
    .await
    .map_err(|e| format!("Failed to create image edit task: {}", e))?;

    Ok(result_url)
}

// Tauri命令：查询豆包图像编辑任务结果
#[tauri::command]
async fn get_doubao_image_edit_task_result(api_key: String, task_id: String) -> Result<serde_json::Value, String> {
    let result = get_doubao_image_edit_result(&api_key, &task_id)
        .await
        .map_err(|e| format!("Failed to get image edit task result: {}", e))?;

    Ok(serde_json::to_value(result).unwrap())
}

// 创建阿里云OutfitAnyone换衣任务
async fn create_outfit_anyone_task(
    api_key: &str,
    person_image_url: &str,
    top_garment_url: Option<&str>,
    bottom_garment_url: Option<&str>,
    resolution: i32,
    restore_face: bool,
) -> Result<String> {
    println!("开始创建阿里云OutfitAnyone换衣任务");
    println!("人物图片URL: {}", person_image_url);
    println!("上装URL: {:?}", top_garment_url);
    println!("下装URL: {:?}", bottom_garment_url);
    println!("分辨率: {}", resolution);
    println!("保留人脸: {}", restore_face);

    let client = reqwest::Client::new();
    let url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/image2image/image-synthesis";

    let request_body = OutfitAnyoneRequest {
        model: "aitryon".to_string(), // 使用基础版模型
        input: OutfitAnyoneInput {
            person_image_url: person_image_url.to_string(),
            top_garment_url: top_garment_url.map(|s| s.to_string()),
            bottom_garment_url: bottom_garment_url.map(|s| s.to_string()),
        },
        parameters: OutfitAnyoneParameters {
            resolution,
            restore_face,
        },
    };

    println!("请求体: {}", serde_json::to_string_pretty(&request_body).unwrap_or_default());

    let response = client
        .post(url)
        .header("Authorization", format!("Bearer {}", api_key))
        .header("Content-Type", "application/json")
        .header("X-DashScope-Async", "enable") // 启用异步调用
        .json(&request_body)
        .send()
        .await?;

    let status = response.status();
    let response_text = response.text().await?;

    println!("创建任务响应状态: {}", status);
    println!("创建任务响应内容: {}", response_text);

    if !status.is_success() {
        return Err(anyhow::anyhow!("Failed to create outfit anyone task: {}", response_text));
    }

    // 解析响应
    let task_response: OutfitAnyoneTaskResponse = serde_json::from_str(&response_text)
        .map_err(|e| anyhow::anyhow!("Failed to parse task response: {}. Response was: {}", e, response_text))?;

    println!("任务创建成功，任务ID: {}", task_response.output.task_id);
    Ok(task_response.output.task_id)
}

// 查询阿里云OutfitAnyone换衣任务结果
async fn get_outfit_anyone_result(api_key: &str, task_id: &str) -> Result<OutfitAnyoneResultResponse> {
    println!("查询阿里云OutfitAnyone换衣任务结果，任务ID: {}", task_id);

    let client = reqwest::Client::new();
    let url = format!("https://dashscope.aliyuncs.com/api/v1/tasks/{}", task_id);

    let response = client
        .get(&url)
        .header("Authorization", format!("Bearer {}", api_key))
        .send()
        .await?;

    let status = response.status();
    let response_text = response.text().await?;

    println!("查询任务响应状态: {}", status);
    println!("查询任务响应内容: {}", response_text);

    if !status.is_success() {
        return Err(anyhow::anyhow!("Failed to get outfit anyone task result: {}", response_text));
    }

    let result: OutfitAnyoneResultResponse = serde_json::from_str(&response_text)
        .map_err(|e| anyhow::anyhow!("Failed to parse outfit anyone task result: {}. Response was: {}", e, response_text))?;

    println!("任务状态: {}", result.output.task_status);
    Ok(result)
}

// Tauri命令：创建阿里云OutfitAnyone换衣任务
#[tauri::command]
async fn create_outfit_anyone_tryon(
    api_key: String,
    person_image_url: String,
    top_garment_url: Option<String>,
    bottom_garment_url: Option<String>,
    resolution: i32,
    restore_face: bool,
) -> Result<String, String> {
    let task_id = create_outfit_anyone_task(
        &api_key,
        &person_image_url,
        top_garment_url.as_deref(),
        bottom_garment_url.as_deref(),
        resolution,
        restore_face,
    )
    .await
    .map_err(|e| format!("Failed to create outfit anyone task: {}", e))?;

    Ok(task_id)
}

// Tauri命令：查询阿里云OutfitAnyone换衣任务结果
#[tauri::command]
async fn get_outfit_anyone_task_result(api_key: String, task_id: String) -> Result<serde_json::Value, String> {
    let result = get_outfit_anyone_result(&api_key, &task_id)
        .await
        .map_err(|e| format!("Failed to get outfit anyone task result: {}", e))?;

    Ok(serde_json::to_value(result).unwrap())
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_dialog::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            upload_file_and_get_url,
            create_doubao_image_edit,
            get_doubao_image_edit_task_result,
            create_outfit_anyone_tryon,
            get_outfit_anyone_task_result
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
