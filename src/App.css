:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  color: #0f0f0f;
  background-color: #f6f6f6;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

#root {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.container h1 {
  text-align: center;
  color: #333;
  margin-bottom: 10px;
}

.container > p {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
}

.error-message {
  background-color: #fee;
  border: 1px solid #fcc;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.error-message p {
  color: #c33;
  margin: 0;
}

.progress-message {
  background-color: #e8f4fd;
  border: 1px solid #bee5eb;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-message p {
  color: #0c5460;
  margin: 0;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.config-section,
.upload-section,
.process-section,
.results-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.config-section h2,
.upload-section h2,
.process-section h2,
.results-section h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  color: #495057;
  font-size: 1.2em;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.api-key-input,
.function-select,
.prompt-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  box-sizing: border-box;
}

.api-key-input:focus,
.function-select:focus,
.prompt-textarea:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.prompt-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.file-upload-area {
  margin-bottom: 15px;
}

.file-select-btn,
.upload-btn,
.process-btn,
.download-btn,
.query-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.file-select-btn {
  background-color: #6c757d;
  color: white;
}

.file-select-btn:hover:not(:disabled) {
  background-color: #5a6268;
}

.file-select-btn.secondary {
  background-color: #17a2b8;
}

.file-select-btn.secondary:hover:not(:disabled) {
  background-color: #138496;
}

.upload-btn {
  background-color: #007bff;
  color: white;
  width: 100%;
  justify-content: center;
}

.upload-btn:hover:not(:disabled) {
  background-color: #0056b3;
}

.process-btn {
  background-color: #28a745;
  color: white;
  width: 100%;
  justify-content: center;
}

.process-btn:hover:not(:disabled) {
  background-color: #1e7e34;
}

.download-btn {
  background-color: #ffc107;
  color: #212529;
  font-size: 12px;
  padding: 8px 12px;
}

.download-btn:hover:not(:disabled) {
  background-color: #e0a800;
}

.query-btn {
  background-color: #17a2b8;
  color: white;
  font-size: 12px;
  padding: 8px 12px;
  margin-top: 10px;
}

.query-btn:hover:not(:disabled) {
  background-color: #138496;
}

.file-select-btn:disabled,
.upload-btn:disabled,
.process-btn:disabled,
.download-btn:disabled,
.query-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.selected-file {
  margin-top: 8px;
  color: #495057;
  font-size: 14px;
}

.upload-success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.upload-success p {
  color: #155724;
  margin: 0;
  word-break: break-all;
}

.task-info {
  background-color: #e2e3e5;
  border: 1px solid #d6d8db;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.task-info p {
  color: #383d41;
  margin: 0;
  font-family: monospace;
}

.result-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.result-item {
  background: white;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-image {
  width: 100%;
  height: auto;
  border-radius: 8px;
  margin-bottom: 10px;
}

.hidden-input {
  display: none;
}

.current-image-info {
  background-color: #f0f8ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.current-image-info p {
  margin: 5px 0;
  color: #333;
}

.current-image-info strong {
  color: #0066cc;
}

.result-images {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.result-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  justify-content: center;
}

/* 标签页样式 */
.tab-container {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 10px;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.tab-button:hover {
  background-color: #e8e8e8;
  color: #333;
}

.tab-button.active {
  background-color: #007bff;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.tab-button.active:hover {
  background-color: #0056b3;
}

/* 任务信息样式 */
.task-info {
  margin-top: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.task-info p {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 14px;
}

.task-info .query-btn {
  margin-top: 10px;
}
