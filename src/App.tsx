import { useState } from "react";
import { invoke } from "@tauri-apps/api/core";
import { Upload, Image, Loader2, Download, Settings, Search } from "lucide-react";
import "./App.css";

// 移除未使用的TaskResult接口

function App() {
  const [selectedFile, setSelectedFile] = useState<string>("");
  const [douBao<PERSON>pi<PERSON><PERSON>, setDouBaoApi<PERSON>ey] = useState<string>("");
  const [prompt, setPrompt] = useState<string>("");
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [uploadedUrl, setUploadedUrl] = useState<string>("");
  const [taskId, setTaskId] = useState<string>("");
  const [resultImages, setResultImages] = useState<string[]>([]);
  const [isQuerying, setIsQuerying] = useState(false);
  const [error, setError] = useState<string>("");
  const [progress, setProgress] = useState<string>("");

  // 图像编辑参数
  const [editStrength, setEditStrength] = useState<number>(0.7);
  const [guidanceScale, setGuidanceScale] = useState<number>(7.5);
  const [inferenceSteps, setInferenceSteps] = useState<number>(20);
  const [randomSeed, setRandomSeed] = useState<string>("");

  // 腾讯云COS配置
  const [cosRegion, setCosRegion] = useState<string>("ap-shanghai");
  const [cosSecretId, setCosSecretId] = useState<string>("AKIDHdmkWCk6g3D9rMeiYCdpz1rVvINHxQis");
  const [cosSecretKey, setCosSecretKey] = useState<string>("3tWv64pOk9XzKXddWJyVwEOQL8oNn6IZ");
  const [cosBucketName, setCosBucketName] = useState<string>("tennis-1258507500");
  const [cosCustomDomain, setCosCustomDomain] = useState<string>("");

  // 手动输入的图片URL
  const [manualImageUrl, setManualImageUrl] = useState<string>("");
  const [urlValidationError, setUrlValidationError] = useState<string>("");

  // 换衣功能相关状态
  const [aliyunApiKey, setAliyunApiKey] = useState<string>("");
  const [personImageUrl, setPersonImageUrl] = useState<string>("");
  const [topGarmentUrl, setTopGarmentUrl] = useState<string>("");
  const [bottomGarmentUrl, setBottomGarmentUrl] = useState<string>("");
  const [resolution, setResolution] = useState<number>(-1);
  const [restoreFace, setRestoreFace] = useState<boolean>(true);
  const [isCreatingTryonTask, setIsCreatingTryonTask] = useState<boolean>(false);
  const [isQueryingTryonResult, setIsQueryingTryonResult] = useState<boolean>(false);
  const [tryonTaskId, setTryonTaskId] = useState<string>("");
  const [tryonResultImages, setTryonResultImages] = useState<string[]>([]);
  const [currentTab, setCurrentTab] = useState<'image-edit' | 'outfit-tryon'>('image-edit');

  const handleFilePathChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedFile(event.target.value);
    setError("");
  };



  // 验证图片URL格式
  const validateImageUrl = (url: string): boolean => {
    if (!url.trim()) return true; // 空URL是允许的

    try {
      const urlObj = new URL(url);
      // 检查协议是否为http或https
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false;
      }

      // 检查是否为图片文件扩展名
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      const pathname = urlObj.pathname.toLowerCase();
      const hasImageExtension = imageExtensions.some(ext => pathname.endsWith(ext));

      return hasImageExtension;
    } catch {
      return false;
    }
  };

  // 处理手动输入URL的变化
  const handleManualUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const url = event.target.value;
    setManualImageUrl(url);

    if (url.trim() && !validateImageUrl(url)) {
      setUrlValidationError("请输入有效的图片URL（支持jpg、png、gif等格式）");
    } else {
      setUrlValidationError("");
    }
  };

  const uploadFile = async () => {
    if (!selectedFile || !cosSecretId || !cosSecretKey || !cosBucketName) {
      setError("请选择文件并配置腾讯云COS参数");
      return;
    }

    setIsUploading(true);
    setError("");
    setProgress("正在上传文件到腾讯云COS...");

    try {
      const url = await invoke<string>("upload_file_and_get_url", {
        region: cosRegion,
        secretId: cosSecretId,
        secretKey: cosSecretKey,
        bucketName: cosBucketName,
        customDomain: cosCustomDomain || null,
        filePath: selectedFile,
      });
      setUploadedUrl(url);
      setProgress("文件上传成功！");
      setTimeout(() => setProgress(""), 2000);
    } catch (err) {
      setError(`上传失败: ${err}`);
    } finally {
      setIsUploading(false);
    }
  };

  const startImageEdit = async () => {
    // 优先使用手动输入的URL，如果为空则使用上传文件的URL
    const imageUrlToUse = manualImageUrl.trim() || uploadedUrl;

    if (!imageUrlToUse || !prompt) {
      setError("请先上传文件或输入图片URL，并输入图像编辑提示词");
      return;
    }

    // 验证手动输入的URL格式
    if (manualImageUrl.trim() && !validateImageUrl(manualImageUrl)) {
      setError("请输入有效的图片URL格式");
      return;
    }

    if (!douBaoApiKey) {
      setError("请输入豆包API密钥");
      return;
    }

    setIsProcessing(true);
    setError("");
    setProgress("正在进行图像编辑...");

    try {
      const seed = randomSeed.trim() ? parseInt(randomSeed) : null;
      const resultUrl = await invoke<string>("create_doubao_image_edit", {
        apiKey: douBaoApiKey,
        imageUrl: imageUrlToUse,
        prompt,
        strength: editStrength,
        guidanceScale,
        numInferenceSteps: inferenceSteps,
        seed,
      });

      // 302.AI直接返回结果URL，不需要轮询
      setResultImages([resultUrl]);
      setProgress("图像编辑完成！");
      setIsProcessing(false);
      setTimeout(() => setProgress(""), 3000);
    } catch (err) {
      setError(`图像编辑失败: ${err}`);
      setIsProcessing(false);
    }
  };

  // 移除未使用的pollTaskResult函数，因为302.AI直接返回结果

  const queryTaskResult = async () => {
    if (!taskId || !douBaoApiKey) {
      setError("请先创建任务并输入豆包API密钥");
      return;
    }

    setIsQuerying(true);
    setError("");
    setProgress("正在查询图像编辑任务状态...");

    try {
      const result = await invoke<any>("get_doubao_image_edit_task_result", {
        apiKey: douBaoApiKey,
        taskId,
      });

      const status = result.status;

      if (status === "completed" || status === "success") {
        if (result.result && result.result.image_url) {
          setResultImages([result.result.image_url]);
          setProgress("图像编辑完成！");
          setTimeout(() => setProgress(""), 3000);
        } else {
          setError("图像编辑完成但未找到图片URL");
        }
      } else if (status === "failed" || status === "error") {
        const errorMsg = result.message || "图像编辑任务失败";
        setError(`任务失败: ${errorMsg}`);
      } else if (status === "pending" || status === "running" || status === "processing") {
        setProgress(`任务状态: ${status === "pending" ? "排队中" : "处理中"}，请稍后再次查询`);
        setTimeout(() => setProgress(""), 3000);
      } else {
        setError(`未知任务状态: ${status}`);
      }
    } catch (err) {
      setError(`查询任务状态失败: ${err}`);
    } finally {
      setIsQuerying(false);
    }
  };

  const downloadImage = async (url: string) => {
    try {
      // 在浏览器中打开图片URL
      window.open(url, '_blank');
    } catch (err) {
      setError(`打开图片失败: ${err}`);
    }
  };

  // 创建换衣任务
  const createTryonTask = async () => {
    if (!aliyunApiKey) {
      setError("请输入阿里云API密钥");
      return;
    }

    if (!personImageUrl) {
      setError("请输入人物图片URL");
      return;
    }

    if (!topGarmentUrl && !bottomGarmentUrl) {
      setError("请至少输入一个服装图片URL（上装或下装）");
      return;
    }

    setIsCreatingTryonTask(true);
    setError("");
    setProgress("正在创建换衣任务...");

    try {
      const taskId = await invoke<string>("create_outfit_anyone_tryon", {
        apiKey: aliyunApiKey,
        personImageUrl,
        topGarmentUrl: topGarmentUrl || null,
        bottomGarmentUrl: bottomGarmentUrl || null,
        resolution,
        restoreFace,
      });

      setTryonTaskId(taskId);
      setProgress("换衣任务创建成功！请点击查询结果按钮获取结果");
      setTimeout(() => setProgress(""), 3000);
    } catch (err) {
      setError(`创建换衣任务失败: ${err}`);
    } finally {
      setIsCreatingTryonTask(false);
    }
  };

  // 查询换衣任务结果
  const queryTryonResult = async () => {
    if (!tryonTaskId || !aliyunApiKey) {
      setError("请先创建任务并输入阿里云API密钥");
      return;
    }

    setIsQueryingTryonResult(true);
    setError("");
    setProgress("正在查询换衣任务状态...");

    try {
      const result = await invoke<any>("get_outfit_anyone_task_result", {
        apiKey: aliyunApiKey,
        taskId: tryonTaskId,
      });

      const status = result.output.task_status;

      if (status === "SUCCEEDED") {
        if (result.output.image_url) {
          setTryonResultImages([result.output.image_url]);
          setProgress("换衣任务完成！");
          setTimeout(() => setProgress(""), 3000);
        } else {
          setError("换衣任务完成但未找到图片URL");
        }
      } else if (status === "FAILED") {
        const errorMsg = result.output.message || "换衣任务失败";
        setError(`任务失败: ${errorMsg}`);
      } else if (["PENDING", "PRE-PROCESSING", "RUNNING", "POST-PROCESSING"].includes(status)) {
        const statusMap: { [key: string]: string } = {
          "PENDING": "排队中",
          "PRE-PROCESSING": "前置处理中",
          "RUNNING": "处理中",
          "POST-PROCESSING": "后置处理中"
        };
        setProgress(`任务状态: ${statusMap[status] || status}，请稍后再次查询`);
        setTimeout(() => setProgress(""), 3000);
      } else {
        setError(`未知任务状态: ${status}`);
      }
    } catch (err) {
      setError(`查询换衣任务状态失败: ${err}`);
    } finally {
      setIsQueryingTryonResult(false);
    }
  };

  return (
    <main className="container">
      <h1>AI 图像处理工具</h1>
      <p>支持图像编辑和AI换衣功能</p>

      {/* 标签页切换 */}
      <div className="tab-container">
        <button
          className={`tab-button ${currentTab === 'image-edit' ? 'active' : ''}`}
          onClick={() => setCurrentTab('image-edit')}
        >
          <Image size={16} />
          图像编辑
        </button>
        <button
          className={`tab-button ${currentTab === 'outfit-tryon' ? 'active' : ''}`}
          onClick={() => setCurrentTab('outfit-tryon')}
        >
          <Upload size={16} />
          AI换衣
        </button>
      </div>

      {error && (
        <div className="error-message">
          <p>错误: {error}</p>
        </div>
      )}

      {progress && (
        <div className="progress-message">
          <Loader2 className="spinner" />
          <p>{progress}</p>
        </div>
      )}

      <div className="config-section">
        <h2><Settings size={20} /> 配置</h2>

        {currentTab === 'image-edit' && (
          <>
            <div className="form-group">
              <label>豆包API密钥:</label>
              <input
                type="password"
                value={douBaoApiKey}
                onChange={(e) => setDouBaoApiKey(e.target.value)}
                placeholder="请输入您的豆包API密钥"
                className="api-key-input"
              />
            </div>
          </>
        )}

        {currentTab === 'outfit-tryon' && (
          <>
            <div className="form-group">
              <label>阿里云API密钥:</label>
              <input
                type="password"
                value={aliyunApiKey}
                onChange={(e) => setAliyunApiKey(e.target.value)}
                placeholder="请输入您的阿里云API密钥"
                className="api-key-input"
              />
            </div>
          </>
        )}

        {currentTab === 'image-edit' && (
          <>
            <h3>图像编辑参数</h3>
            <div className="form-group">
              <label>编辑强度 (0.0-1.0):</label>
              <input
                type="number"
                min="0.0"
                max="1.0"
                step="0.1"
                value={editStrength}
                onChange={(e) => setEditStrength(parseFloat(e.target.value))}
                className="api-key-input"
                placeholder="0.7"
              />
              <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
                💡 值越高，编辑效果越明显
              </p>
            </div>

            <div className="form-group">
              <label>引导比例 (1.0-20.0):</label>
              <input
                type="number"
                min="1.0"
                max="20.0"
                step="0.5"
                value={guidanceScale}
                onChange={(e) => setGuidanceScale(parseFloat(e.target.value))}
                className="api-key-input"
                placeholder="7.5"
              />
              <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
                💡 控制模型对提示词的遵循程度
              </p>
            </div>

            <div className="form-group">
              <label>推理步数 (10-50):</label>
              <input
                type="number"
                min="10"
                max="50"
                step="1"
                value={inferenceSteps}
                onChange={(e) => setInferenceSteps(parseInt(e.target.value))}
                className="api-key-input"
                placeholder="20"
              />
              <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
                💡 步数越多，质量越高但速度越慢
              </p>
            </div>

            <div className="form-group">
              <label>随机种子 (可选):</label>
              <input
                type="text"
                value={randomSeed}
                onChange={(e) => setRandomSeed(e.target.value)}
                className="api-key-input"
                placeholder="留空则随机生成"
              />
              <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
                💡 相同种子可以生成相似的结果
              </p>
            </div>
          </>
        )}

        {currentTab === 'outfit-tryon' && (
          <>
            <h3>换衣参数</h3>
            <div className="form-group">
              <label>输出分辨率:</label>
              <select
                value={resolution}
                onChange={(e) => setResolution(parseInt(e.target.value))}
                className="api-key-input"
              >
                <option value={-1}>与原图保持一致</option>
                <option value={1024}>576x1024</option>
                <option value={1280}>720x1280</option>
              </select>
              <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
                💡 选择输出图片的分辨率
              </p>
            </div>

            <div className="form-group">
              <label>保留人脸:</label>
              <select
                value={restoreFace.toString()}
                onChange={(e) => setRestoreFace(e.target.value === 'true')}
                className="api-key-input"
              >
                <option value="true">保留原图人脸</option>
                <option value="false">随机生成新人脸</option>
              </select>
              <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
                💡 是否保留模特图中的人脸
              </p>
            </div>
          </>
        )}

        {currentTab === 'image-edit' && (
          <>
            <h3>腾讯云COS配置</h3>
            <div className="form-group">
              <label>区域 (Region):</label>
              <input
                type="text"
                value={cosRegion}
                onChange={(e) => setCosRegion(e.target.value)}
                placeholder="例如: ap-shanghai"
                className="cos-config-input"
              />
            </div>

            <div className="form-group">
              <label>访问凭据ID (Secret ID):</label>
              <input
                type="password"
                value={cosSecretId}
                onChange={(e) => setCosSecretId(e.target.value)}
                placeholder="请输入腾讯云COS Secret ID"
                className="cos-config-input"
              />
            </div>

            <div className="form-group">
              <label>凭据密钥 (Secret Key):</label>
              <input
                type="password"
                value={cosSecretKey}
                onChange={(e) => setCosSecretKey(e.target.value)}
                placeholder="请输入腾讯云COS Secret Key"
                className="cos-config-input"
              />
            </div>

            <div className="form-group">
              <label>存储桶名称 (Bucket Name):</label>
              <input
                type="text"
                value={cosBucketName}
                onChange={(e) => setCosBucketName(e.target.value)}
                placeholder="例如: tennis-1258507500"
                className="cos-config-input"
              />
            </div>

            <div className="form-group">
              <label>自定义域名 (可选):</label>
              <input
                type="text"
                value={cosCustomDomain}
                onChange={(e) => setCosCustomDomain(e.target.value)}
                placeholder="例如: cdn.example.com"
                className="cos-config-input"
              />
            </div>
          </>
        )}

        {currentTab === 'image-edit' && (
          <div className="form-group">
            <label>图像编辑提示词:</label>
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="请输入图像编辑的提示词，例如：将天空改为夕阳，保持其他部分不变"
              className="prompt-textarea"
              rows={3}
            />
          </div>
        )}
      </div>

      {currentTab === 'image-edit' && (
        <div className="upload-section">
          <h2><Upload size={20} /> 图片来源</h2>

          <div className="form-group">
            <label>图片URL（可选）:</label>
            <input
              type="text"
              value={manualImageUrl}
              onChange={handleManualUrlChange}
              placeholder="直接输入图片URL，例如: https://example.com/image.jpg"
              className="api-key-input"
            />
            {urlValidationError && (
              <div className="error-message" style={{marginTop: "5px", fontSize: "14px"}}>
                <p>{urlValidationError}</p>
              </div>
            )}
            <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
              💡 提示：可以直接输入图片URL，或者上传本地文件到腾讯云COS
            </p>
          </div>
        </div>
      )}

      {currentTab === 'outfit-tryon' && (
        <div className="upload-section">
          <h2><Upload size={20} /> 换衣图片配置</h2>

          <div className="form-group">
            <label>人物图片URL:</label>
            <input
              type="text"
              value={personImageUrl}
              onChange={(e) => setPersonImageUrl(e.target.value)}
              placeholder="请输入人物全身正面照的URL"
              className="api-key-input"
            />
            <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
              💡 需要人物全身正面照，光照良好，有且仅有一个完整的人
            </p>
          </div>

          <div className="form-group">
            <label>上装图片URL（可选）:</label>
            <input
              type="text"
              value={topGarmentUrl}
              onChange={(e) => setTopGarmentUrl(e.target.value)}
              placeholder="请输入上装平铺图的URL"
              className="api-key-input"
            />
            <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
              💡 服装平铺拍摄，背景简洁，无折叠遮挡
            </p>
          </div>

          <div className="form-group">
            <label>下装图片URL（可选）:</label>
            <input
              type="text"
              value={bottomGarmentUrl}
              onChange={(e) => setBottomGarmentUrl(e.target.value)}
              placeholder="请输入下装平铺图的URL"
              className="api-key-input"
            />
            <p style={{fontSize: "12px", color: "#666", marginTop: "5px"}}>
              💡 至少需要提供上装或下装中的一个
            </p>
          </div>
        </div>
      )}

      {currentTab === 'image-edit' && (
        <div className="upload-section">
          <div className="form-group">
            <label>本地图片文件路径:</label>
            <input
              type="text"
              value={selectedFile}
              onChange={handleFilePathChange}
              placeholder="请输入图片文件的完整路径，例如: /Users/<USER>/Pictures/image.jpg"
              className="api-key-input"
            />
          </div>

          <button
            type="button"
            onClick={uploadFile}
            disabled={!selectedFile || !cosSecretId || !cosSecretKey || !cosBucketName || isUploading}
            className="upload-btn"
          >
            {isUploading ? <Loader2 className="spinner" /> : <Upload size={20} />}
            {isUploading ? "上传中..." : "上传到腾讯云COS"}
          </button>

          {uploadedUrl && (
            <div className="upload-success">
              <p>✅ 文件上传成功！URL: {uploadedUrl}</p>
            </div>
          )}
        </div>
      )}

      {currentTab === 'image-edit' && (
        <div className="process-section">
          <h2><Settings size={20} /> 图像编辑</h2>

          {/* 显示当前使用的图片来源 */}
          {(manualImageUrl.trim() || uploadedUrl) && (
            <div className="current-image-info">
              <p><strong>当前图片来源：</strong></p>
              {manualImageUrl.trim() ? (
                <p>🔗 手动输入URL: {manualImageUrl}</p>
              ) : (
                <p>📁 上传文件URL: {uploadedUrl}</p>
              )}
            </div>
          )}

          <button
            type="button"
            onClick={startImageEdit}
            disabled={(!uploadedUrl && !manualImageUrl.trim()) || !prompt || !douBaoApiKey || isProcessing || !!urlValidationError}
            className="process-btn"
          >
            {isProcessing ? <Loader2 className="spinner" /> : <Image size={20} />}
            {isProcessing ? "编辑中..." : "开始图像编辑"}
          </button>
        </div>
      )}

      {currentTab === 'outfit-tryon' && (
        <div className="process-section">
          <h2><Settings size={20} /> AI换衣</h2>

          <button
            type="button"
            onClick={createTryonTask}
            disabled={!aliyunApiKey || !personImageUrl || (!topGarmentUrl && !bottomGarmentUrl) || isCreatingTryonTask}
            className="process-btn"
          >
            {isCreatingTryonTask ? <Loader2 className="spinner" /> : <Image size={20} />}
            {isCreatingTryonTask ? "创建任务中..." : "创建换衣任务"}
          </button>

          {tryonTaskId && (
            <div className="task-info">
              <p><strong>任务ID:</strong> {tryonTaskId}</p>
              <button
                type="button"
                onClick={queryTryonResult}
                disabled={isQueryingTryonResult}
                className="query-btn"
              >
                {isQueryingTryonResult ? <Loader2 className="spinner" /> : <Search size={20} />}
                {isQueryingTryonResult ? "查询中..." : "查询任务结果"}
              </button>
            </div>
          )}
        </div>
      )}

      {currentTab === 'image-edit' && (
        <div className="task-section">
          <div className="form-group">
            <label>任务ID:</label>
            <input
              type="text"
              value={taskId}
              onChange={(e) => setTaskId(e.target.value)}
              placeholder="任务ID会在创建任务后自动填入，也可手动输入"
              className="api-key-input"
            />
          </div>

          {taskId && (
            <button
              type="button"
              onClick={queryTaskResult}
              disabled={!taskId || !douBaoApiKey || isQuerying}
              className="query-btn"
            >
              {isQuerying ? <Loader2 className="spinner" /> : <Search size={16} />}
              {isQuerying ? "查询中..." : "查询任务结果"}
            </button>
          )}
        </div>
      )}

      {currentTab === 'image-edit' && resultImages.length > 0 && (
        <div className="results-section">
          <h2><Image size={20} /> 图像编辑结果</h2>
          <div className="result-images">
            {resultImages.map((url, index) => (
              <div key={index} className="result-item">
                <img
                  src={url}
                  alt={`编辑结果 ${index + 1}`}
                  className="result-image"
                />
                <div className="image-actions">
                  <button
                    onClick={() => downloadImage(url)}
                    className="download-btn"
                    type="button"
                  >
                    <Download size={16} />
                    查看原图
                  </button>
                  <button
                    onClick={() => window.open(url, '_blank')}
                    className="download-btn"
                    type="button"
                  >
                    <Image size={16} />
                    在新窗口打开
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {currentTab === 'outfit-tryon' && tryonResultImages.length > 0 && (
        <div className="results-section">
          <h2><Image size={20} /> AI换衣结果</h2>
          <div className="result-images">
            {tryonResultImages.map((url, index) => (
              <div key={index} className="result-item">
                <img
                  src={url}
                  alt={`换衣结果 ${index + 1}`}
                  className="result-image"
                />
                <div className="image-actions">
                  <button
                    onClick={() => downloadImage(url)}
                    className="download-btn"
                    type="button"
                  >
                    <Download size={16} />
                    查看原图
                  </button>
                  <button
                    onClick={() => window.open(url, '_blank')}
                    className="download-btn"
                    type="button"
                  >
                    <Image size={16} />
                    在新窗口打开
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </main>
  );
}

export default App;
